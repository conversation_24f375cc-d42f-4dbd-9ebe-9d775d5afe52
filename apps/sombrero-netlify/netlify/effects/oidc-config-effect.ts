import { Cache, Duration, Effect } from "effect";
import { z } from "zod";

/** Schema for OIDC configuration response */
const OidcConfigSchema = z.object({
	authorization_endpoint: z.string().url().optional(),
	end_session_endpoint: z.string().url().optional(),
	issuer: z.string().url(),
	jwks_uri: z.string().url(),
	token_endpoint: z.string().url().optional(),
	userinfo_endpoint: z.string().url().optional(),
});

export type OidcConfig = z.infer<typeof OidcConfigSchema>;

/** Schema for JWK (JSON Web Key) from JWKS endpoint */
const JwkSchema = z.object({
	alg: z.string().optional(), // Algorithm
	// EC specific fields
	crv: z.string().optional(), // Curve
	e: z.string().optional(), // Exponent
	key_ops: z.array(z.string()).optional(), // Key operations
	kid: z.string().optional(), // Key ID
	kty: z.string(), // Key type
	// RSA specific fields
	n: z.string().optional(), // Modulus
	use: z.string().optional(), // Public key use
	x: z.string().optional(), // X coordinate
	x5c: z.array(z.string()).optional(), // X.509 certificate chain
	x5t: z.string().optional(), // X.509 thumbprint
	"x5t#S256": z.string().optional(), // X.509 thumbprint SHA-256
	x5u: z.string().optional(), // X.509 URL
	y: z.string().optional(), // Y coordinate
});

/** Schema for JWKS (JSON Web Key Set) response */
const JwksSchema = z.object({
	keys: z.array(JwkSchema),
});

export type Jwk = z.infer<typeof JwkSchema>;
export type Jwks = z.infer<typeof JwksSchema>;

/** Determines the expected issuer URL based on the current environment */
export const getExpectedIssuerEffect = Effect.try({
	catch: (error) => `Get Expected Issuer Effect Error: ${String(error)}`,
	try: () => {
		const loginUrl = process.env.CIAM_LOGIN_API_URL;

		if (!loginUrl) {
			throw new Error("CIAM_LOGIN_API_URL environment variable is not set");
		}

		// Extract tenant ID from login URL pattern
		// Expected format: https://login-{env}.anwb.nl/{tenant-id}/login
		const urlMatch = /https:\/\/([^/]+)\/([^/]+)/.exec(loginUrl);

		if (!urlMatch) {
			throw new Error(`Invalid CIAM_LOGIN_API_URL format: ${loginUrl}`);
		}

		const [, host, tenantId] = urlMatch;

		// Return the issuer URL in the expected format
		return `https://${host ?? ""}/${tenantId ?? ""}/login`;
	},
});

/** Fetches OIDC configuration from the well-known endpoint */
const fetchOidcConfigEffect = (issuerUrl: string) =>
	Effect.tryPromise({
		catch: (error) => `Fetch OIDC Config Effect Error: ${String(error)}`,
		try: async () => {
			const configUrl = `${issuerUrl}/.well-known/openid-configuration`;

			const response = await fetch(configUrl, {
				headers: {
					Accept: "application/json",
					"User-Agent": "ANWB-Sombrero-Netlify/1.0",
				},
			});

			if (!response.ok) {
				throw new Error(
					`Failed to fetch OIDC config: ${response.status} ${response.statusText}`,
				);
			}

			const data = await response.json();
			return OidcConfigSchema.parse(data);
		},
	});

/** Fetches JWK public keys from the JWKS endpoint */
const fetchJwksEffect = (jwksUri: string) =>
	Effect.tryPromise({
		catch: (error) => `Fetch JWKS Effect Error: ${String(error)}`,
		try: async () => {
			const response = await fetch(jwksUri, {
				headers: {
					Accept: "application/json",
					"User-Agent": "ANWB-Sombrero-Netlify/1.0",
				},
			});

			if (!response.ok) {
				throw new Error(
					`Failed to fetch JWKS: ${response.status} ${response.statusText}`,
				);
			}

			const data = await response.json();
			return JwksSchema.parse(data);
		},
	});

/** Create OIDC configuration cache */
const oidcConfigCacheEffect = Cache.make({
	capacity: 100,
	lookup: (issuerUrl: string) => fetchOidcConfigEffect(issuerUrl),
	timeToLive: Duration.hours(1),
});

/** Create JWKS cache */
const jwksCacheEffect = Cache.make({
	capacity: 100,
	lookup: (jwksUri: string) => fetchJwksEffect(jwksUri),
	timeToLive: Duration.hours(1),
});

/** Cached OIDC configuration effect */
export const oidcConfigEffect = (issuerUrl: string) =>
	Effect.gen(function* () {
		const cache = yield* oidcConfigCacheEffect;
		return yield* cache.get(issuerUrl);
	});

/** Cached JWKS effect */
export const jwksEffect = (jwksUri: string) =>
	Effect.gen(function* () {
		const cache = yield* jwksCacheEffect;
		return yield* cache.get(jwksUri);
	});

/** Combined effect to get OIDC config and JWKS */
export const oidcConfigAndJwksEffect = (issuerUrl: string) =>
	Effect.gen(function* () {
		const config = yield* oidcConfigEffect(issuerUrl);
		const jwks = yield* jwksEffect(config.jwks_uri);

		return { config, jwks };
	});

/** Finds a JWK by key ID (kid) from the JWKS */
export const findJwkByKidEffect = (jwks: Jwks, kid?: string) =>
	Effect.try({
		catch: () => "JWK not found" as const,
		try: (): Jwk => {
			if (!kid) {
				// If no kid specified, return the first key
				const firstKey = jwks.keys[0];
				if (!firstKey) {
					throw new Error("No keys available in JWKS");
				}
				return firstKey;
			}

			const key = jwks.keys.find((k) => k.kid === kid);
			if (!key) {
				throw new Error(`No key found with kid: ${kid}`);
			}
			return key;
		},
	});

/** Complete effect to get expected issuer, OIDC config, and JWKS */
export const completeOidcEffect = Effect.gen(function* () {
	const issuerUrl = yield* getExpectedIssuerEffect;
	const { config, jwks } = yield* oidcConfigAndJwksEffect(issuerUrl);

	return {
		config,
		issuerUrl,
		jwks,
	};
});

/** Effect to find a specific JWK by kid */
export const findJwkEffect = (kid?: string) =>
	Effect.gen(function* () {
		const { jwks } = yield* completeOidcEffect;
		return yield* findJwkByKidEffect(jwks, kid);
	});
