import { Cache, Duration, Effect } from "effect";

import {
	type Jwk,
	type Jwks,
	JwksSchema,
	OidcConfigSchema,
} from "../schemas/oidc-schema";

/** Determines the expected issuer URL based on the current environment */
export const getExpectedIssuerEffect = Effect.tryPromise({
	catch: (error) => `Get Expected Issuer Effect Error: ${String(error)}`,
	try: async () => {
		const loginUrl = process.env.CIAM_LOGIN_API_URL;

		if (!loginUrl) {
			throw new Error("CIAM_LOGIN_API_URL environment variable is not set");
		}

		// Extract tenant ID from login URL pattern
		// Expected format: https://login-{env}.anwb.nl/{tenant-id}/login
		const urlMatch = /https:\/\/([^/]+)\/([^/]+)/.exec(loginUrl);

		if (!urlMatch) {
			throw new Error(`Invalid CIAM_LOGIN_API_URL format: ${loginUrl}`);
		}

		const [, host, tenantId] = urlMatch;

		// Return the issuer URL in the expected format
		return `https://${host ?? ""}/${tenantId ?? ""}/login`;
	},
});

/** Fetches OIDC configuration from the well-known endpoint */
const fetchOidcConfigEffect = (issuerUrl: string) =>
	Effect.tryPromise({
		catch: (error) => `Fetch OIDC Config Effect Error: ${String(error)}`,
		try: async () => {
			const configUrl = `${issuerUrl}/.well-known/openid-configuration`;

			const response = await fetch(configUrl, {
				headers: {
					Accept: "application/json",
					"User-Agent": "ANWB-Sombrero-Netlify/1.0",
				},
			});

			if (!response.ok) {
				throw new Error(
					`Failed to fetch OIDC config: ${response.status} ${response.statusText}`,
				);
			}

			const data = await response.json();
			return OidcConfigSchema.parse(data);
		},
	});

/** Fetches JWK public keys from the JWKS endpoint */
const fetchJwksEffect = (jwksUri: string) =>
	Effect.tryPromise({
		catch: (error) => `Fetch JWKS Effect Error: ${String(error)}`,
		try: async () => {
			const response = await fetch(jwksUri, {
				headers: {
					Accept: "application/json",
					"User-Agent": "ANWB-Sombrero-Netlify/1.0",
				},
			});

			if (!response.ok) {
				throw new Error(
					`Failed to fetch JWKS: ${response.status} ${response.statusText}`,
				);
			}

			const data = await response.json();
			return JwksSchema.parse(data);
		},
	});

/** Create OIDC configuration cache */
const oidcConfigCacheEffect = Cache.make({
	capacity: 100,
	lookup: (issuerUrl: string) => fetchOidcConfigEffect(issuerUrl),
	timeToLive: Duration.hours(1),
});

/** Create JWKS cache */
const jwksCacheEffect = Cache.make({
	capacity: 100,
	lookup: (jwksUri: string) => fetchJwksEffect(jwksUri),
	timeToLive: Duration.hours(1),
});

/** Cached OIDC configuration effect */
export const oidcConfigEffect = (issuerUrl: string) =>
	oidcConfigCacheEffect.pipe(Effect.flatMap((cache) => cache.get(issuerUrl)));

/** Cached JWKS effect */
export const jwksEffect = (jwksUri: string) =>
	jwksCacheEffect.pipe(Effect.flatMap((cache) => cache.get(jwksUri)));

/** Combined effect to get OIDC config and JWKS */
export const oidcConfigAndJwksEffect = (issuerUrl: string) =>
	oidcConfigEffect(issuerUrl).pipe(
		Effect.flatMap((config) =>
			jwksEffect(config.jwks_uri).pipe(
				Effect.map((jwks) => ({ config, jwks })),
			),
		),
	);

/** Finds a JWK by key ID (kid) from the JWKS */
export const findJwkByKidEffect = (jwks: Jwks, kid?: string) =>
	Effect.tryPromise({
		catch: () => "JWK not found" as const,
		try: async (): Promise<Jwk> => {
			if (!kid) {
				// If no kid specified, return the first key
				const firstKey = jwks.keys[0];
				if (!firstKey) {
					throw new Error("No keys available in JWKS");
				}
				return firstKey;
			}

			const key = jwks.keys.find((k) => k.kid === kid);
			if (!key) {
				throw new Error(`No key found with kid: ${kid}`);
			}
			return key;
		},
	});

/** Complete effect to get expected issuer, OIDC config, and JWKS */
export const completeOidcEffect = getExpectedIssuerEffect.pipe(
	Effect.flatMap((issuerUrl) =>
		oidcConfigAndJwksEffect(issuerUrl).pipe(
			Effect.map(({ config, jwks }) => ({
				config,
				issuerUrl,
				jwks,
			})),
		),
	),
);

/** Effect to find a specific JWK by kid */
export const findJwkEffect = (kid?: string) =>
	completeOidcEffect.pipe(
		Effect.flatMap(({ jwks }) => findJwkByKidEffect(jwks, kid)),
	);
