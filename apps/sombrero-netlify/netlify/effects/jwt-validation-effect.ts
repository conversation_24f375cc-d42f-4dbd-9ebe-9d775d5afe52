import { Effect } from "effect";
import { importJWK, jwtVerify } from "jose";

import {
	JwtClaimsSchema,
	type JwtValidationError,
	type JwtValidationResult,
} from "../schemas/jwt-claims-schema";
import { completeOidcEffect, findJwkByKidEffect } from "./oidc-config-effect";

/** Parse JWT header to extract key ID (kid) */
const parseJwtHeaderEffect = (accessToken: string) =>
	Effect.tryPromise({
		catch: () => "JWT_PARSE_ERROR" as const,
		try: async () => {
			const jwtParts = accessToken.split(".");
			if (jwtParts.length !== 3) {
				throw new Error("Invalid JWT format");
			}

			const headerPart = jwtParts[0];
			if (!headerPart) {
				throw new Error("Missing JWT header");
			}

			const header = JSON.parse(atob(headerPart));
			return header.kid as string | undefined;
		},
	});

/** Convert JWK to KeyLike object for jose */
const importJwkEffect = (jwk: any) =>
	Effect.tryPromise({
		catch: () => "JWT_SIGNATURE_INVALID" as const,
		try: () => importJWK(jwk),
	});

/** Verify JWT signature and extract payload */
const verifyJwtSignatureEffect = (
	accessToken: string,
	publicKey: any,
	issuerUrl: string,
) =>
	Effect.tryPromise({
		catch: (error) => {
			console.error("JWT verification failed:", error);

			// Determine specific error type
			if (error instanceof Error) {
				if (error.message.includes("expired")) {
					return "JWT_EXPIRED" as const;
				}
				if (error.message.includes("issuer")) {
					return "JWT_ISSUER_MISMATCH" as const;
				}
				if (error.message.includes("signature")) {
					return "JWT_SIGNATURE_INVALID" as const;
				}
			}

			return "JWT_SIGNATURE_INVALID" as const;
		},
		try: async () => {
			const { payload: jwtPayload } = await jwtVerify(accessToken, publicKey, {
				issuer: issuerUrl,
				// Note: We don't validate audience here as it may vary
				// clockTolerance: 30, // Allow 30 seconds clock skew
			});
			return jwtPayload;
		},
	});

/** Validate JWT claims structure */
const validateJwtClaimsEffect = (payload: any) =>
	Effect.tryPromise({
		catch: (error) => {
			console.error("JWT claims validation failed:", error);
			return "JWT_CLAIMS_INVALID" as const;
		},
		try: async () => JwtClaimsSchema.parse(payload),
	});

/** Validate JWT claims timing and issuer */
const validateJwtTimingAndIssuerEffect = (claims: any, issuerUrl: string) =>
	Effect.tryPromise({
		catch: (error) => String(error),
		try: async () => {
			const now = Math.floor(Date.now() / 1000);

			// Check expiration
			if (claims.exp <= now) {
				throw new Error("JWT_EXPIRED");
			}

			// Check not before (if present)
			if (claims.nbf && claims.nbf > now) {
				throw new Error("JWT_NOT_YET_VALID");
			}

			// Check issuer matches expected
			if (claims.iss !== issuerUrl) {
				console.error(
					`Issuer mismatch: expected ${issuerUrl}, got ${String(claims.iss)}`,
				);
				throw new Error("JWT_ISSUER_MISMATCH");
			}

			return claims;
		},
	}).pipe(
		Effect.mapError((errorMessage) => {
			// Extract the error type from the Error message
			if (errorMessage.includes("JWT_EXPIRED")) return "JWT_EXPIRED" as const;
			if (errorMessage.includes("JWT_NOT_YET_VALID"))
				return "JWT_NOT_YET_VALID" as const;
			if (errorMessage.includes("JWT_ISSUER_MISMATCH"))
				return "JWT_ISSUER_MISMATCH" as const;
			return "JWT_CLAIMS_INVALID" as const;
		}),
	);

/**
 * JWT validation Effect that uses cached OIDC configuration
 *
 * This is the main validation function that performs comprehensive JWT
 * validation:
 *
 * 1. JWT parsing and structure validation
 * 2. Signature verification using OIDC public keys (cached)
 * 3. Claims validation (iss, exp, iat)
 * 4. Expiration time checking
 *
 * This Effect never fails - all errors are converted to validation results.
 *
 * @param accessToken - The JWT access token to validate
 * @returns Effect that resolves to validation result (never fails)
 */
export const validateAccessTokenJwtEffect = (accessToken: string) =>
	Effect.Do.pipe(
		// Parse JWT header to extract key ID
		Effect.bind("kid", () => parseJwtHeaderEffect(accessToken)),

		// Get OIDC configuration and JWKS
		Effect.bind("oidcData", () => completeOidcEffect),

		// Find the matching JWK by key ID
		Effect.bind("jwk", ({ kid, oidcData }) =>
			findJwkByKidEffect(oidcData.jwks, kid),
		),

		// Convert JWK to KeyLike object for jose
		Effect.bind("publicKey", ({ jwk }) => importJwkEffect(jwk)),

		// Verify JWT signature and extract payload
		Effect.bind("payload", ({ oidcData, publicKey }) =>
			verifyJwtSignatureEffect(accessToken, publicKey, oidcData.issuerUrl),
		),

		// Validate JWT claims structure
		Effect.bind("claims", ({ payload }) => validateJwtClaimsEffect(payload)),

		// Validate timing and issuer
		Effect.bind("validatedClaims", ({ claims, oidcData }) =>
			validateJwtTimingAndIssuerEffect(claims, oidcData.issuerUrl),
		),

		Effect.map(
			({ validatedClaims }) =>
				({
					claims: validatedClaims,
					success: true,
				}) as const,
		),

		Effect.catchAll((error) =>
			Effect.succeed({
				error: typeof error === "string" ? error : "JWT_PARSE_ERROR",
				success: false,
			} as const),
		),
	);

/**
 * Promise wrapper for validateAccessTokenJwtEffect
 *
 * This is a convenience function that runs the Effect and returns a Promise.
 * Use validateAccessTokenJwtEffect directly when working with Effects.
 *
 * @param accessToken - The JWT access token to validate
 * @returns Promise resolving to validation result (never throws)
 */
export const validateAccessTokenJwt = (
	accessToken: string,
): Promise<JwtValidationResult> =>
	Effect.runPromise(
		validateAccessTokenJwtEffect(accessToken),
	) as Promise<JwtValidationResult>;

/**
 * Creates appropriate login URL based on JWT validation error
 *
 * @param baseLoginUrl - The base login URL
 * @param error - The JWT validation error
 * @returns Complete login URL with appropriate parameters
 */
export const createLoginUrlForError = (
	baseLoginUrl: string,
	error: JwtValidationError,
): string => {
	const url = new URL(baseLoginUrl);

	if (error !== "JWT_EXPIRED") {
		url.searchParams.set("prompt", "login");
	}

	return url.toString();
};
