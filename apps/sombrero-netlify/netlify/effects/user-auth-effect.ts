import { Effect, Option } from "effect";

import type { SombreroData } from "../schemas/sombrero-data-schema";

import { fetchBloomreachCmsTokenStatus } from "../helpers/fetch-bloomreach-cms-token-status";
import { isNextCmsHost } from "../helpers/is-next-cms-host";
import {
	createLoginUrlForError,
	validateAccessTokenJwtSafe,
} from "./jwt-validation-effect";

/**
 * Creates a login endpoint URL with proper redirect configuration
 *
 * @param origin - The origin URL of the application
 * @param resolvedUrl - The resolved URL path from the request
 * @returns Effect that resolves to the complete login URL with redirect
 *   parameters
 */
const getLoginEndpointEffect = (origin?: string, resolvedUrl?: string) =>
	Effect.tryPromise({
		catch: (error) => `Get Login Endpoint Effect Error: ${error as Error}`,
		try: async () => {
			if (!origin || !resolvedUrl) {
				throw new Error(
					"x-application-origin header is not set. Aborting request",
				);
			}

			const loginToken = process.env.CIAM_LOGIN_API_KEY ?? "";

			const redirectUrl = new URL(`${origin}${resolvedUrl}`);

			const redirectUri = `${origin}${redirectUrl.pathname}${redirectUrl.search}`;

			const url = new URL(process.env.CIAM_LOGIN_API_URL ?? "");

			url.searchParams.append("client_id", loginToken);
			url.searchParams.append("redirect_uri", redirectUri);

			return url.toString();
		},
	});

/**
 * Handles authentication validation for Bloomreach CMS requests
 *
 * @param origin - The origin URL of the application
 * @param resolvedUrl - The resolved URL path from the request
 * @param loginEndpoint - Optional login endpoint URL for redirects
 * @returns Effect that resolves to Option with authentication status and
 *   redirect URL
 */
const handleBloomreachAuthEffect = (
	origin: string,
	resolvedUrl: string,
	loginEndpoint?: string,
) =>
	Effect.tryPromise({
		catch: (error) => `Handle Bloomreach Auth Effect Error: ${error as Error}`,
		try: async () => {
			const url = new URL(`${origin}${resolvedUrl}`);

			const validCmsToken = await fetchBloomreachCmsTokenStatus(url);

			const isValidCmsToken = Boolean(validCmsToken);

			if (!isValidCmsToken) {
				return Option.some({
					isValid: false,
					redirectUrl: loginEndpoint,
				});
			}

			return Option.none();
		},
	});

/**
 * Extracts access token from AccessToken cookie
 *
 * @param cookies - All cookies from the request
 * @returns Access token string or undefined if not found
 */
const getAccessToken = (cookies: Partial<Record<string, string>>) => {
	return cookies.AccessToken;
};

type UserAuthEffect = {
	sombreroData: SombreroData;
};

/**
 * Main authentication effect that validates user authentication state
 *
 * This effect:
 *
 * 1. Creates a login endpoint URL for redirects
 * 2. Handles special authentication for Bloomreach CMS requests
 * 3. Extracts access tokens from cookies or headers
 * 4. Validates token with authentication service
 * 5. Returns authentication status with redirect URL if needed
 *
 * @param params - The parameters for the effect
 * @param params.sombreroData - Request data including cookies, headers, and URL
 *   information
 * @returns Effect that resolves to Option containing authentication status and
 *   redirect information
 */
export const userAuthEffect = ({ sombreroData }: UserAuthEffect) =>
	Effect.tryPromise({
		catch: (error) => `User Auth Effect Error: ${error as Error}`,
		try: async () => {
			const { cookies, headers, resolvedUrl } = sombreroData;

			const origin = process.env.NEXT_PUBLIC_BASE_URL ?? sombreroData.origin;

			const refererHeader = headers.referer;

			const loginEndpoint = getLoginEndpointEffect(
				origin.toString(),
				resolvedUrl,
			)
				.pipe(Effect.andThen((data) => data))
				.pipe(Effect.runSync);

			if (refererHeader && isNextCmsHost(refererHeader)) {
				return Effect.runPromise(
					handleBloomreachAuthEffect(origin, resolvedUrl, loginEndpoint),
				);
			}

			const accessToken = getAccessToken(cookies);

			if (!accessToken) {
				return Option.some({
					isValid: false,
					redirectUrl: loginEndpoint,
				});
			}

			// Validate JWT locally instead of external API call
			const jwtValidationResult = await validateAccessTokenJwtSafe(accessToken);

			if (!jwtValidationResult.success) {
				// Create appropriate login URL based on the specific error
				const redirectUrl = createLoginUrlForError(
					loginEndpoint,
					jwtValidationResult.error,
				);

				return Option.some({
					isValid: false,
					redirectUrl,
				});
			}

			// JWT is valid - user is authenticated
			return Option.some({
				isValid: true,
				redirectUrl: "",
			});
		},
	});
