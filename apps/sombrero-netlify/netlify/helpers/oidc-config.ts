import { z } from "zod";

/** Schema for OIDC configuration response */
const OidcConfigSchema = z.object({
	authorization_endpoint: z.string().url().optional(),
	end_session_endpoint: z.string().url().optional(),
	issuer: z.string().url(),
	jwks_uri: z.string().url(),
	token_endpoint: z.string().url().optional(),
	userinfo_endpoint: z.string().url().optional(),
});

export type OidcConfig = z.infer<typeof OidcConfigSchema>;

/** Schema for JWK (JSON Web Key) from JWKS endpoint */
const JwkSchema = z.object({
	alg: z.string().optional(), // Algorithm
	// EC specific fields
	crv: z.string().optional(), // Curve
	e: z.string().optional(), // Exponent
	key_ops: z.array(z.string()).optional(), // Key operations
	kid: z.string().optional(), // Key ID
	kty: z.string(), // Key type
	// RSA specific fields
	n: z.string().optional(), // Modulus
	use: z.string().optional(), // Public key use
	x: z.string().optional(), // X coordinate
	x5c: z.array(z.string()).optional(), // X.509 certificate chain
	x5t: z.string().optional(), // X.509 thumbprint
	"x5t#S256": z.string().optional(), // X.509 thumbprint SHA-256
	x5u: z.string().optional(), // X.509 URL
	y: z.string().optional(), // Y coordinate
});

/** Schema for JWKS (JSON Web Key Set) response */
const JwksSchema = z.object({
	keys: z.array(JwkSchema),
});

export type Jwk = z.infer<typeof JwkSchema>;
export type Jwks = z.infer<typeof JwksSchema>;

/** Cache entry structure for storing OIDC configuration and JWK data */
type CacheEntry<T> = {
	data: T;
	expires: number; // Unix timestamp
};

/**
 * In-memory cache for OIDC configuration and JWK data Cache TTL is set to 1
 * hour (3600 seconds)
 */
const CACHE_TTL_MS = 60 * 60 * 1000; // 1 hour
const oidcConfigCache = new Map<string, CacheEntry<OidcConfig>>();
const jwksCache = new Map<string, CacheEntry<Jwks>>();

/**
 * Determines the expected issuer URL based on the current environment
 *
 * @returns The expected issuer URL for JWT validation
 */
export const getExpectedIssuer = (): string => {
	const loginUrl = process.env.CIAM_LOGIN_API_URL;

	if (!loginUrl) {
		throw new Error("CIAM_LOGIN_API_URL environment variable is not set");
	}

	// Extract tenant ID from login URL pattern
	// Expected format: https://login-{env}.anwb.nl/{tenant-id}/login
	const urlMatch = /https:\/\/([^\/]+)\/([^\/]+)/.exec(loginUrl);

	if (!urlMatch) {
		throw new Error(`Invalid CIAM_LOGIN_API_URL format: ${loginUrl}`);
	}

	const [, host, tenantId] = urlMatch;

	// Return the issuer URL in the expected format
	return `https://${host}/${tenantId}/login`;
};

/**
 * Fetches OIDC configuration from the well-known endpoint with caching
 *
 * @param issuerUrl - The issuer URL to fetch configuration for
 * @returns Promise resolving to OIDC configuration
 */
export const fetchOidcConfig = async (
	issuerUrl: string,
): Promise<OidcConfig> => {
	// Check cache first
	const cached = oidcConfigCache.get(issuerUrl);
	const now = Date.now();

	if (cached && cached.expires > now) {
		return cached.data;
	}

	try {
		const configUrl = `${issuerUrl}/.well-known/openid-configuration`;

		const response = await fetch(configUrl, {
			headers: {
				Accept: "application/json",
				"User-Agent": "ANWB-Sombrero-Netlify/1.0",
			},
		});

		if (!response.ok) {
			throw new Error(
				`Failed to fetch OIDC config: ${response.status} ${response.statusText}`,
			);
		}

		const data = await response.json();
		const config = OidcConfigSchema.parse(data);

		// Cache the result
		oidcConfigCache.set(issuerUrl, {
			data: config,
			expires: now + CACHE_TTL_MS,
		});

		return config;
	} catch (error) {
		console.error("Error fetching OIDC configuration:", error);
		throw new Error(
			`Failed to fetch OIDC configuration: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
};

/**
 * Fetches JWK public keys from the JWKS endpoint with caching
 *
 * @param jwksUri - The JWKS URI to fetch keys from
 * @returns Promise resolving to JWKS data
 */
export const fetchJwks = async (jwksUri: string): Promise<Jwks> => {
	// Check cache first
	const cached = jwksCache.get(jwksUri);
	const now = Date.now();

	if (cached && cached.expires > now) {
		return cached.data;
	}

	try {
		const response = await fetch(jwksUri, {
			headers: {
				Accept: "application/json",
				"User-Agent": "ANWB-Sombrero-Netlify/1.0",
			},
		});

		if (!response.ok) {
			throw new Error(
				`Failed to fetch JWKS: ${response.status} ${response.statusText}`,
			);
		}

		const data = await response.json();
		const jwks = JwksSchema.parse(data);

		// Cache the result
		jwksCache.set(jwksUri, {
			data: jwks,
			expires: now + CACHE_TTL_MS,
		});

		return jwks;
	} catch (error) {
		console.error("Error fetching JWKS:", error);
		throw new Error(
			`Failed to fetch JWKS: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
};

/**
 * Finds a JWK by key ID (kid) from the JWKS
 *
 * @param jwks - The JWKS data
 * @param kid - The key ID to find
 * @returns The matching JWK or undefined if not found
 */
export const findJwkByKid = (jwks: Jwks, kid?: string): Jwk | undefined => {
	if (!kid) {
		// If no kid specified, return the first key
		return jwks.keys[0];
	}

	return jwks.keys.find((key) => key.kid === kid);
};

/**
 * Clears the OIDC configuration and JWKS caches Useful for testing or forcing
 * cache refresh
 */
export const clearOidcCache = (): void => {
	oidcConfigCache.clear();
	jwksCache.clear();
};
