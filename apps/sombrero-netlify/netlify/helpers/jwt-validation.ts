import { jwtVerify, importJW<PERSON>, type JWTPayload } from "jose";
import { 
	JwtClaimsSchema, 
	type JwtClaims, 
	type JwtValidationResult, 
	type JwtValidationError 
} from "../schemas/jwt-claims-schema";
import { 
	fetchOidcConfig, 
	fetchJwks, 
	findJwkByKid, 
	getExpectedIssuer,
	type Jwk 
} from "./oidc-config";

/**
 * Validates an AccessToken JWT locally without external API calls
 * 
 * This function performs comprehensive JWT validation including:
 * 1. JWT parsing and structure validation
 * 2. Signature verification using OIDC public keys
 * 3. Claims validation (iss, exp, iat)
 * 4. Expiration time checking
 * 
 * @param accessToken - The JWT access token to validate
 * @returns Promise resolving to validation result
 */
export const validateAccessTokenJwt = async (accessToken: string): Promise<JwtValidationResult> => {
	try {
		// Get expected issuer for this environment
		const expectedIssuer = getExpectedIssuer();
		
		// Fetch OIDC configuration
		const oidcConfig = await fetchOidcConfig(expectedIssuer);
		
		// Fetch JWK public keys
		const jwks = await fetchJwks(oidcConfig.jwks_uri);
		
		// Parse JWT header to get key ID (kid)
		const jwtParts = accessToken.split('.');
		if (jwtParts.length !== 3) {
			return { success: false, error: "JWT_PARSE_ERROR" };
		}
		
		let kid: string | undefined;
		try {
			const header = JSON.parse(atob(jwtParts[0]));
			kid = header.kid;
		} catch {
			return { success: false, error: "JWT_PARSE_ERROR" };
		}
		
		// Find matching JWK
		const jwk = findJwkByKid(jwks, kid);
		if (!jwk) {
			console.error(`No matching JWK found for kid: ${kid}`);
			return { success: false, error: "JWT_SIGNATURE_INVALID" };
		}
		
		// Convert JWK to KeyLike object for jose
		const publicKey = await importJWK(jwk as any);
		
		// Verify JWT signature and extract payload
		let payload: JWTPayload;
		try {
			const { payload: jwtPayload } = await jwtVerify(accessToken, publicKey, {
				issuer: expectedIssuer,
				// Note: We don't validate audience here as it may vary
				// clockTolerance: 30, // Allow 30 seconds clock skew
			});
			payload = jwtPayload;
		} catch (error) {
			console.error("JWT verification failed:", error);
			
			// Determine specific error type
			if (error instanceof Error) {
				if (error.message.includes("expired")) {
					return { success: false, error: "JWT_EXPIRED" };
				}
				if (error.message.includes("issuer")) {
					return { success: false, error: "JWT_ISSUER_MISMATCH" };
				}
				if (error.message.includes("signature")) {
					return { success: false, error: "JWT_SIGNATURE_INVALID" };
				}
			}
			
			return { success: false, error: "JWT_SIGNATURE_INVALID" };
		}
		
		// Validate claims structure
		let claims: JwtClaims;
		try {
			claims = JwtClaimsSchema.parse(payload);
		} catch (error) {
			console.error("JWT claims validation failed:", error);
			return { success: false, error: "JWT_CLAIMS_INVALID" };
		}
		
		// Additional manual validations
		const now = Math.floor(Date.now() / 1000);
		
		// Check expiration
		if (claims.exp <= now) {
			return { success: false, error: "JWT_EXPIRED" };
		}
		
		// Check not before (if present)
		if (claims.nbf && claims.nbf > now) {
			return { success: false, error: "JWT_NOT_YET_VALID" };
		}
		
		// Check issuer matches expected
		if (claims.iss !== expectedIssuer) {
			console.error(`Issuer mismatch: expected ${expectedIssuer}, got ${claims.iss}`);
			return { success: false, error: "JWT_ISSUER_MISMATCH" };
		}
		
		return { success: true, claims };
		
	} catch (error) {
		console.error("JWT validation error:", error);
		
		// Handle specific OIDC configuration errors
		if (error instanceof Error) {
			if (error.message.includes("CIAM_LOGIN_API_URL")) {
				console.error("CIAM configuration error:", error.message);
			}
			if (error.message.includes("OIDC")) {
				console.error("OIDC configuration error:", error.message);
			}
		}
		
		return { success: false, error: "JWT_PARSE_ERROR" };
	}
};

/**
 * Determines if a JWT validation error should trigger a redirect with prompt=login
 * 
 * @param error - The JWT validation error
 * @returns true if should redirect with prompt=login, false for redirect without prompt
 */
export const shouldRedirectWithPrompt = (error: JwtValidationError): boolean => {
	// Only expired tokens should redirect without prompt=login to allow token refresh
	return error !== "JWT_EXPIRED";
};

/**
 * Creates appropriate login URL based on JWT validation error
 * 
 * @param baseLoginUrl - The base login URL
 * @param error - The JWT validation error
 * @returns Complete login URL with appropriate parameters
 */
export const createLoginUrlForError = (baseLoginUrl: string, error: JwtValidationError): string => {
	const url = new URL(baseLoginUrl);
	
	if (shouldRedirectWithPrompt(error)) {
		url.searchParams.set("prompt", "login");
	}
	
	return url.toString();
};

/**
 * Higher-order function that wraps JWT validation with error handling
 * 
 * This wrapper catches any errors thrown by the JWT validation and returns
 * a standardized error result instead of throwing.
 * 
 * @param accessToken - The JWT access token to validate
 * @returns Promise resolving to validation result (never throws)
 */
export const validateAccessTokenJwtSafe = async (accessToken: string): Promise<JwtValidationResult> => {
	try {
		return await validateAccessTokenJwt(accessToken);
	} catch (error) {
		console.error("Unexpected error in JWT validation:", error);
		return { success: false, error: "JWT_PARSE_ERROR" };
	}
};
