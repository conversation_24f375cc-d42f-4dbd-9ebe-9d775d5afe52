import { Effect } from "effect";
import { importJWK, type JWTPayload, jwtVerify } from "jose";

import {
	completeOidcEffect,
	findJwkByKidEffect,
} from "../effects/oidc-config-effect";
import {
	type JwtClaims,
	JwtClaimsSchema,
	type JwtValidationError,
	type JwtValidationResult,
} from "../schemas/jwt-claims-schema";

/** Effect-based JWT validation that uses cached OIDC configuration */
const validateAccessTokenJwtEffect = (accessToken: string) =>
	Effect.gen(function* () {
		// Parse JWT header to get key ID (kid)
		const jwtParts = accessToken.split(".");
		if (jwtParts.length !== 3) {
			return { error: "JWT_PARSE_ERROR", success: false } as const;
		}

		let kid: string | undefined;
		try {
			const headerPart = jwtParts[0];
			if (!headerPart) {
				return { error: "JWT_PARSE_ERROR", success: false } as const;
			}
			const header = JSON.parse(atob(headerPart));
			kid = header.kid;
		} catch {
			return { error: "JWT_PARSE_ERROR", success: false } as const;
		}

		// Get OIDC configuration and JWKS (cached)
		const { issuerUrl, jwks } = yield* completeOidcEffect;

		// Find matching JWK
		const jwk = yield* findJwkByKidEffect(jwks, kid);

		// Convert JWK to KeyLike object for jose
		const publicKey = yield* Effect.tryPromise({
			catch: () => "JWT_SIGNATURE_INVALID" as const,
			try: () => importJWK(jwk as any),
		});

		// Verify JWT signature and extract payload
		const payload = yield* Effect.tryPromise({
			catch: (error) => {
				console.error("JWT verification failed:", error);

				// Determine specific error type
				if (error instanceof Error) {
					if (error.message.includes("expired")) {
						return "JWT_EXPIRED" as const;
					}
					if (error.message.includes("issuer")) {
						return "JWT_ISSUER_MISMATCH" as const;
					}
					if (error.message.includes("signature")) {
						return "JWT_SIGNATURE_INVALID" as const;
					}
				}

				return "JWT_SIGNATURE_INVALID" as const;
			},
			try: async () => {
				const { payload: jwtPayload } = await jwtVerify(
					accessToken,
					publicKey,
					{
						issuer: issuerUrl,
						// Note: We don't validate audience here as it may vary
						// clockTolerance: 30, // Allow 30 seconds clock skew
					},
				);
				return jwtPayload;
			},
		});

		// Validate claims structure
		const claims = yield* Effect.try({
			catch: (error) => {
				console.error("JWT claims validation failed:", error);
				return "JWT_CLAIMS_INVALID" as const;
			},
			try: () => JwtClaimsSchema.parse(payload),
		});

		// Additional manual validations
		const now = Math.floor(Date.now() / 1000);

		// Check expiration
		if (claims.exp <= now) {
			return { error: "JWT_EXPIRED", success: false } as const;
		}

		// Check not before (if present)
		if (claims.nbf && claims.nbf > now) {
			return { error: "JWT_NOT_YET_VALID", success: false } as const;
		}

		// Check issuer matches expected
		if (claims.iss !== issuerUrl) {
			console.error(
				`Issuer mismatch: expected ${issuerUrl}, got ${claims.iss}`,
			);
			return { error: "JWT_ISSUER_MISMATCH", success: false } as const;
		}

		return { claims, success: true } as const;
	});

/**
 * Validates an AccessToken JWT locally without external API calls
 *
 * This function performs comprehensive JWT validation including:
 *
 * 1. JWT parsing and structure validation
 * 2. Signature verification using OIDC public keys (cached)
 * 3. Claims validation (iss, exp, iat)
 * 4. Expiration time checking
 *
 * @param accessToken - The JWT access token to validate
 * @returns Promise resolving to validation result
 */
export const validateAccessTokenJwt = async (
	accessToken: string,
): Promise<JwtValidationResult> => {
	const result = await Effect.runPromise(
		validateAccessTokenJwtEffect(accessToken).pipe(
			Effect.catchAll((error) =>
				Effect.succeed({
					error: typeof error === "string" ? error : "JWT_PARSE_ERROR",
					success: false,
				} as const),
			),
		),
	);

	return result as JwtValidationResult;
};

/**
 * Determines if a JWT validation error should trigger a redirect with
 * prompt=login
 *
 * @param error - The JWT validation error
 * @returns True if should redirect with prompt=login, false for redirect
 *   without prompt
 */
export const shouldRedirectWithPrompt = (
	error: JwtValidationError,
): boolean => {
	// Only expired tokens should redirect without prompt=login to allow token refresh
	return error !== "JWT_EXPIRED";
};

/**
 * Creates appropriate login URL based on JWT validation error
 *
 * @param baseLoginUrl - The base login URL
 * @param error - The JWT validation error
 * @returns Complete login URL with appropriate parameters
 */
export const createLoginUrlForError = (
	baseLoginUrl: string,
	error: JwtValidationError,
): string => {
	const url = new URL(baseLoginUrl);

	if (shouldRedirectWithPrompt(error)) {
		url.searchParams.set("prompt", "login");
	}

	return url.toString();
};

/**
 * Higher-order function that wraps JWT validation with error handling
 *
 * This wrapper catches any errors thrown by the JWT validation and returns a
 * standardized error result instead of throwing.
 *
 * @param accessToken - The JWT access token to validate
 * @returns Promise resolving to validation result (never throws)
 */
export const validateAccessTokenJwtSafe = async (
	accessToken: string,
): Promise<JwtValidationResult> => {
	try {
		return await validateAccessTokenJwt(accessToken);
	} catch (error) {
		console.error("Unexpected error in JWT validation:", error);
		return { error: "JWT_PARSE_ERROR", success: false };
	}
};
