import { z } from "zod";

/**
 * Schema for OIDC configuration response
 * 
 * This schema validates the OpenID Connect configuration response from the
 * /.well-known/openid-configuration endpoint. It includes all standard OIDC
 * endpoints and configuration parameters.
 */
export const OidcConfigSchema = z.object({
	/**
	 * Authorization endpoint URL - where users are redirected to authenticate
	 */
	authorization_endpoint: z.string().url().optional(),

	/**
	 * End session endpoint URL - where users are redirected to logout
	 */
	end_session_endpoint: z.string().url().optional(),

	/**
	 * Issuer URL - identifies the principal that issued the JWT
	 * This must match the 'iss' claim in JWT tokens
	 */
	issuer: z.string().url(),

	/**
	 * JSON Web Key Set URI - endpoint containing public keys for JWT verification
	 */
	jwks_uri: z.string().url(),

	/**
	 * Token endpoint URL - where authorization codes are exchanged for tokens
	 */
	token_endpoint: z.string().url().optional(),

	/**
	 * UserInfo endpoint URL - where user information can be retrieved
	 */
	userinfo_endpoint: z.string().url().optional(),
});

export type OidcConfig = z.infer<typeof OidcConfigSchema>;

/**
 * Schema for JWK (JSON Web Key) from JWKS endpoint
 * 
 * This schema validates individual JSON Web Keys used for JWT signature verification.
 * It supports both RSA and EC (Elliptic Curve) key types with their respective parameters.
 */
export const JwkSchema = z.object({
	/**
	 * Algorithm intended for use with the key
	 */
	alg: z.string().optional(),

	/**
	 * Curve name for Elliptic Curve keys (e.g., "P-256", "P-384", "P-521")
	 */
	crv: z.string().optional(),

	/**
	 * Exponent for RSA public keys (typically "AQAB" for 65537)
	 */
	e: z.string().optional(),

	/**
	 * Key operations that the key is intended to be used for
	 */
	key_ops: z.array(z.string()).optional(),

	/**
	 * Key ID - used to match keys with JWT headers
	 */
	kid: z.string().optional(),

	/**
	 * Key type - "RSA" for RSA keys, "EC" for Elliptic Curve keys
	 */
	kty: z.string(),

	/**
	 * Modulus for RSA public keys (base64url-encoded)
	 */
	n: z.string().optional(),

	/**
	 * Public key use - "sig" for signature verification, "enc" for encryption
	 */
	use: z.string().optional(),

	/**
	 * X coordinate for Elliptic Curve public keys (base64url-encoded)
	 */
	x: z.string().optional(),

	/**
	 * X.509 certificate chain (array of base64-encoded DER certificates)
	 */
	x5c: z.array(z.string()).optional(),

	/**
	 * X.509 certificate SHA-1 thumbprint (base64url-encoded)
	 */
	x5t: z.string().optional(),

	/**
	 * X.509 certificate SHA-256 thumbprint (base64url-encoded)
	 */
	"x5t#S256": z.string().optional(),

	/**
	 * X.509 URL pointing to a resource for the X.509 public key certificate
	 */
	x5u: z.string().optional(),

	/**
	 * Y coordinate for Elliptic Curve public keys (base64url-encoded)
	 */
	y: z.string().optional(),
});

export type Jwk = z.infer<typeof JwkSchema>;

/**
 * Schema for JWKS (JSON Web Key Set) response
 * 
 * This schema validates the response from the JWKS endpoint, which contains
 * an array of JSON Web Keys used for JWT signature verification.
 */
export const JwksSchema = z.object({
	/**
	 * Array of JSON Web Keys
	 */
	keys: z.array(JwkSchema),
});

export type Jwks = z.infer<typeof JwksSchema>;
