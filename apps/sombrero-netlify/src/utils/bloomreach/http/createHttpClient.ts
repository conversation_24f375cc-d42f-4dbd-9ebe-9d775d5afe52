import type { IncomingMessage } from "node:http";

import axios, { type AxiosRequestConfig } from "axios-https-proxy-fix";

import type { OrchestratorResponse } from "../../netlify/schemas/orchestratorSchema";

import { env } from "../../../env/server";
import cookie from "../../cookie";
import { getOrigin } from "../../getOrigin";
import { QUERY_PARAMETERS_WHITELIST } from "./constants";
import {
	getBlueConicSegmentSearchParams,
	getOptimizelySearchParams,
	getPersonalizationSearchParams,
	getWhitelistedSearchParams,
} from "./searchParams";

/**
 * Creates a proxy configuration object for axios if proxy settings are defined
 * in the environment.
 *
 * @returns Proxy configuration object or undefined if proxy settings are not
 *   available
 */
export const getProxy = () => {
	const { PROXY_HOST, PROXY_PORT } = env;

	if (!PROXY_HOST || !PROXY_PORT) {
		return undefined;
	}

	return {
		host: PROXY_HOST,
		port: PROXY_PORT,
	};
};

/**
 * Extracts the base URL from an axios configuration by removing query
 * parameters.
 *
 * @param axiosConfig - The axios request configuration
 * @returns The clean URL without query parameters
 */
export const getUrl = (axiosConfig: AxiosRequestConfig) => {
	const [url] = axiosConfig.url?.split("?") ?? [];

	return url;
};

/**
 * Builds search parameters for HTTP requests by combining various parameter
 * sources.
 *
 * Combines:
 *
 * - Base parameters (preflight)
 * - Optimizely parameters
 * - Personalization parameters for components
 * - BlueConic segment parameters
 * - Whitelisted query parameters from the original request
 *
 * @param request - The incoming HTTP request
 * @param orchestratorResponse - Complete orchestrator response object
 * @returns URLSearchParams containing all combined parameters
 */
export const getParams = (
	request: IncomingMessage,
	orchestratorResponse: OrchestratorResponse,
) => {
	const searchParams = new URLSearchParams({ preflight: "false" });

	// Get all URLSearchParams objects
	const optimizelyParams = getOptimizelySearchParams(
		orchestratorResponse.personalization,
	);
	const personalizationParams = getPersonalizationSearchParams(
		orchestratorResponse.personalization,
		orchestratorResponse.hasPersonalization,
	);
	const blueConicParams = getBlueConicSegmentSearchParams(
		orchestratorResponse.personalization,
	);
	const whitelistedParams = getWhitelistedSearchParams(
		request.url || "",
		QUERY_PARAMETERS_WHITELIST,
	);

	// Combine all URLSearchParams by creating a single array of key-value pairs
	const allEntries = [
		...Array.from(searchParams.entries()),
		...Array.from(optimizelyParams.entries()),
		...Array.from(personalizationParams.entries()),
		...Array.from(blueConicParams.entries()),
		...Array.from(whitelistedParams.entries()),
	];

	// Create a new URLSearchParams object from all entries
	return new URLSearchParams(allEntries);
};

/**
 * Extracts and formats cookies from the request headers.
 *
 * Specifically handles the access token from either:
 *
 * - X-sombrero-auth header
 * - AccessToken cookie
 *
 * @param request - The incoming HTTP request
 * @returns Cookie string for the HTTP headers or undefined if no cookies
 */
export const getCookie = (request: IncomingMessage) => {
	const cookies = cookie.deserialize(request.headers.cookie ?? "");
	const bloomreachCookie: Record<string, string> = {};

	const accessToken = request.headers["x-sombrero-auth"] ?? cookies.AccessToken;

	if (typeof accessToken === "string") {
		bloomreachCookie.AccessToken = accessToken;
	}

	return cookie.serialize(bloomreachCookie);
};

/**
 * Creates an HTTP client configured with personalization, cookies, and other
 * request parameters.
 *
 * The returned function allows making HTTP requests with consistent
 * configuration:
 *
 * - Proper cookies and authentication
 * - API key for Bloomreach
 * - Origin information
 * - Personalization parameters
 * - Proxy configuration (if available)
 * - Standard timeout
 *
 * @param request - The incoming HTTP request
 * @param orchestratorResponse - Complete orchestrator response object
 * @returns Function that accepts axios config and returns a promise with the
 *   response
 */
export const createHttpClient = (
	request: IncomingMessage,
	orchestratorResponse: OrchestratorResponse,
) => {
	const proxy = getProxy();
	const cookie = getCookie(request) ?? "";
	const origin = getOrigin(request);
	const params = getParams(request, orchestratorResponse);

	return async (defaultConfig: AxiosRequestConfig) => {
		const url = getUrl(defaultConfig);

		return axios({
			...defaultConfig,
			headers: {
				...defaultConfig.headers,
				Cookie: cookie,
				"x-api-key": env.BLOOMREACH_API_KEY,
				"x-sombrero-origin": origin,
			},
			params,
			proxy,
			timeout: 30_000,
			url,
			withCredentials: false,
		});
	};
};
